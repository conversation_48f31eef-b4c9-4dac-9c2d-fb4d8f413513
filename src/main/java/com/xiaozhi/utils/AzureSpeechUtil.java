package com.xiaozhi.utils;

import com.microsoft.cognitiveservices.speech.*;
import com.microsoft.cognitiveservices.speech.audio.AudioConfig;
import io.vavr.concurrent.Future;
import io.vavr.control.Try;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.net.URI;
import java.net.URISyntaxException;
import java.util.UUID;
import java.util.concurrent.ExecutionException;

@Slf4j
@Component
public class AzureSpeechUtil {

    private static final String endpoint = "https://southeastasia.tts.speech.microsoft.com";
    private static final String secret = "1b8HjkGguTG4OFOk8SeWljidFzddhVPGWGLYSU93zjaLKhW2dUwpJQQJ99BHACqBBLyXJ3w3AAAAACOGuoH2";

    public static Try<String> speak(String content, String voice) throws URISyntaxException, ExecutionException, InterruptedException {
        return Try.of(() -> SpeechConfig.fromEndpoint(new URI(endpoint), secret))
                .flatMap(config -> {
                    config.setSpeechSynthesisVoiceName(voice);
                    var filepath = STR."audio/\{UUID.randomUUID().toString()}.wav";
                    try (var synthesizer = new SpeechSynthesizer(config, AudioConfig.fromWavFileOutput(filepath))) {
                        return Future.fromJavaFuture(synthesizer.SpeakTextAsync(content))
                                .toTry()
                                .onFailure(Throwable::printStackTrace)
                                .filter(it -> it.getReason() == ResultReason.SynthesizingAudioCompleted)
                                .onFailure(Throwable::printStackTrace)
                                .map(result -> {
                                    result.close();
                                    return filepath;
                                });
                    }
                });
    }

    public static Try<String> speakSsml(String content, String voice, String lang) {
        return Try.of(() -> SpeechConfig.fromEndpoint(new URI(endpoint), secret))
                .flatMap(config -> {
                    var ssmlText = buildSSML(removeSpeakTags(content), voice, lang);
                    var filepath = STR."audio/\{UUID.randomUUID().toString()}.wav";
                    try (var synthesizer = new SpeechSynthesizer(config, AudioConfig.fromWavFileOutput(filepath))) {
                        return Future.fromJavaFuture(synthesizer.SpeakSsmlAsync(ssmlText))
                                .toTry()
                                .onFailure(Throwable::printStackTrace)
                                .filter(it -> it.getReason() == ResultReason.SynthesizingAudioCompleted)
                                .onFailure(Throwable::printStackTrace)
                                .map(result -> {
                                    result.close();
                                    return filepath;
                                });
                    }
                });
    }

    public static String buildSSML(String text, String voice, String lang) {
        return STR."""
            <speak version="1.0" xmlns="http://www.w3.org/2001/10/synthesis" xml:lang="\{lang}">
                <voice name="\{voice}">
                    \{text}
                </voice>
            </speak>
            """;
    }

    private static String removeSpeakTags(String input) {
        if (input == null || input.isEmpty()) {
            return input;
        }

        // 移除开头的 <speak> 标签和结尾的 </speak> 标签
        var result = input.replaceAll("(?i)<speak[^>]*>", "")
                .replaceAll("(?i)</speak>", "");

        return result.trim();
    }

}
