package com.xiaozhi.schedule;

import com.xiaozhi.dialogue.llm.providers.CozeChatModel;
import com.xiaozhi.service.FileResourceService;
import com.xiaozhi.service.MediaService;
import com.xiaozhi.utils.AzureSpeechUtil;
import com.xiaozhi.utils.ImageUtil;
import com.xiaozhi.utils.JsonUtil;
import com.xiaozhi.vo.MediaCreateParams;
import io.vavr.control.Try;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.io.FileInputStream;
import java.net.URL;
import java.nio.file.Paths;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.UUID;

@Slf4j
@Component
public class NewsGenerateJob {

    @Resource
    private MediaService mediaService;

    @Resource
    private FileResourceService fileResourceService;

    private final DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy年MM月dd日");

    @Scheduled(cron = "0 0 6 * * ?")
    public void subscribe() {
        var chatModel = new CozeChatModel("7537147450772373544", "sat_DKYZIpfVz7NPw3i3U88msfgwWye4uZbtCn4M1tZkC94nIG9wIVnKjtbdiUA3Yzbq");
        var topic = "生成今日资讯";
        var resp = chatModel.call(topic);

        if (StringUtils.isBlank(resp)) return;

        JsonUtil.parse(resp, Content.class)
                .onFailure(e -> log.error(e.getMessage()))
                .onSuccess(it -> {
                    var media = new MediaCreateParams()
                            .setTitle(STR."\{formatter.format(LocalDate.now())}资讯")
                            .setCategory("news");

                    log.info("Story is {}", it);

                    AzureSpeechUtil.speakSsml(it.story, "zh-CN-YunxiaNeural", "zh-CN")
                            .flatMap(filepath -> Try.of(() -> new FileInputStream(filepath))
                                    .flatMap(is -> fileResourceService.upload("news", Paths.get(filepath).getFileName().toString(), is).toTry()))
                            .peek(media::setAssetId);

                    ImageUtil.generate(it.cover, "512*512")
                            .flatMap(url -> Try.of(() -> new URL(url).openStream()).flatMap(is -> fileResourceService.upload("news", STR."\{UUID.randomUUID().toString()}.jpg", is).toTry()))
                            .peek(media::setCoverId);

                    mediaService.create(media);
                });
    }

    record Content(String story, String cover) {
    }

}
