package com.xiaozhi.service.impl;

import com.xiaozhi.common.web.BizError;
import com.xiaozhi.common.web.OhMyLambdaQueryWrapper;
import com.xiaozhi.config.OssConfig;
import com.xiaozhi.dao.FileResourceMapper;
import com.xiaozhi.dto.FileInfo;
import com.xiaozhi.entity.FileResource;
import com.xiaozhi.service.FileResourceService;
import com.xiaozhi.utils.OssUtil;
import io.vavr.control.Either;
import io.vavr.control.Option;
import io.vavr.control.Try;
import jakarta.annotation.Resource;
import org.apache.commons.lang3.RandomStringUtils;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.InputStream;
import java.net.URLConnection;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;

@Service
public class FileResourceServiceImpl implements FileResourceService {

    @Resource
    private OssUtil ossUtil;

    @Resource
    private OssConfig ossConfig;

    @Resource
    private FileResourceMapper fileResourceMapper;

    @Override
    public Either<BizError, Integer> upload(String space, MultipartFile file) {
        var filename = genFileName(file.getOriginalFilename());
        var object = STR."\{space}/\{filename}";
        return Try.of(file::getInputStream)
                .flatMap(stream -> ossUtil.upload(object, stream))
                .map(__ -> {
                    var entity = new FileResource()
                            .setSpace(space)
                            .setFileName(filename)
                            .setFileUrl(STR."https://\{ossConfig.getBucket()}.oss-cn-beijing.aliyuncs.com/\{space}/\{filename}")
                            .setFileFormerName(file.getOriginalFilename())
                            .setFileType(file.getContentType())
                            .setStorageType("private");
                    fileResourceMapper.insert(entity);
                    return entity.getId();
                })
                .toEither(BizError.UploadFailed);
    }

    @Override
    public Either<BizError, Integer> upload(String space, String filename, InputStream inputStream) {
        var newFileName = genFileName(filename);
        var object = STR."\{space}/\{newFileName}";
        return ossUtil.upload(object, inputStream)
                .map(_ -> {
                    var entity = new FileResource()
                            .setSpace(space)
                            .setFileName(newFileName)
                            .setFileFormerName(filename)
                            .setFileType(URLConnection.guessContentTypeFromName(filename))
                            .setStorageType("public");
                    fileResourceMapper.insert(entity);
                    return entity.getId();
                })
                .toEither(BizError.UploadFailed);
    }

    @Override
    public Either<BizError, Integer> uploadXlsx(String space, String filename, InputStream inputStream) {
        var newFileName = genFileName(filename);
        var object = STR."\{space}/\{newFileName}";
        return ossUtil.upload(object, inputStream)
                .map(__ -> {
                    var entity = new FileResource()
                            .setSpace(space)
                            .setFileName(newFileName)
                            .setFileFormerName(filename)
                            .setFileType("xlsx")
                            .setStorageType("private");
                    fileResourceMapper.insert(entity);
                    return entity.getId();
                })
                .toEither(BizError.UploadFailed);
    }

    @Override
    public Either<BizError, String> sign(String object) {
        return ossUtil.sign(object, 30).toEither(BizError.BadRequest);
    }

    @Override
    public Either<BizError, ?> detail(String space, Integer id) {
        var query = new OhMyLambdaQueryWrapper<FileResource>()
                .eq(FileResource::getId, id)
                .eq(FileResource::getSpace, space)
                .select(FileResource::getFileName, FileResource::getFileFormerName, FileResource::getFileType);
//        return Option.of(fileResourceDao.selectOne(query))
//                .toEither(BizError.NotFoundData)
//                .flatMap(file -> this.sign(STR."\{space}/\{file.getFileName()}")
//                        .map(url -> new FileInfo()
//                                .setFileUrl(url)
//                                .setFileName(file.getFileFormerName())
//                                .setFileType(file.getFileType())));
        return Option.of(fileResourceMapper.selectOne(query))
                .toEither(BizError.ResourceNotFound)
                .map(file ->  new FileInfo()
                        .setFileUrl(STR."https://\{ossConfig.getBucket()}.oss-cn-beijing.aliyuncs.com/\{space}/\{file.getFileName()}")
                        .setFileName(file.getFileFormerName())
                        .setFileType(file.getFileType()));
    }

    private String genFileName(String filename) {
        var df = DateTimeFormatter.ofPattern("yyyyMMdd");
        var ext = filename.substring(filename.lastIndexOf("."));
        return STR."\{df.format(LocalDate.now())}-\{RandomStringUtils.randomAlphabetic(8)}\{ext}";
    }

}
